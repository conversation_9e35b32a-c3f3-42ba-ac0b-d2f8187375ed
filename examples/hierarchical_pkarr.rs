use anyhow::Result;
use ed25519_dalek::{Signing<PERSON><PERSON>, Verifying<PERSON>ey};
use iroh_topic_tracker::integrations::pkarr::{PkarrClient, PublicExtendedKey};
use pkarr::Keypair;

// Simplified key derivation for this example
use curve25519_dalek::{scalar::Scalar, constants::ED25519_BASEPOINT_POINT};
use hmac::{Hmac, Mac};
use sha2::{Digest, Sha256, Sha512};
type HmacSha512 = Hmac<Sha512>;

const CHAIN_CODE_LENGTH: usize = 32;
const HARDENED_OFFSET: u32 = 0x80000000;

#[derive(Debug, <PERSON>lone)]
pub struct ExtendedKey {
    key_left: [u8; 32],
    key_right: [u8; 32],
    chain_code: [u8; CHAIN_CODE_LENGTH],
    public_key: VerifyingKey,
}

#[derive(Debug)]
pub enum KeyDerivationError {
    InvalidKey,
    CryptoError,
}

impl std::fmt::Display for KeyDerivationError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            KeyDerivationError::InvalidKey => write!(f, "Invalid key (third highest bit set)"),
            KeyDerivationError::CryptoError => write!(f, "Cryptographic error"),
        }
    }
}

impl std::error::Error for KeyDerivationError {}

impl ExtendedKey {
    pub fn from_seed(seed: &[u8]) -> Result<Self, KeyDerivationError> {
        let mut mac = HmacSha512::new_from_slice(b"ed25519 seed")
            .map_err(|_| KeyDerivationError::CryptoError)?;
        mac.update(seed);
        let master_key = mac.finalize().into_bytes();
        
        let mut key_left = [0u8; 32];
        let mut key_right = [0u8; 32];
        key_left.copy_from_slice(&master_key[..32]);
        key_right.copy_from_slice(&master_key[32..]);
        
        if (key_left[31] & 0x20) != 0 {
            return Err(KeyDerivationError::InvalidKey);
        }
        
        Self::clamp_scalar(&mut key_left);
        
        let scalar = Scalar::from_bytes_mod_order(key_left);
        let public_point = &scalar * &ED25519_BASEPOINT_POINT;
        let public_key = VerifyingKey::from_bytes(&public_point.compress().to_bytes())
            .map_err(|_| KeyDerivationError::CryptoError)?;
        
        let mut chain_hasher = Sha256::new();
        chain_hasher.update(&[0x01]);
        chain_hasher.update(&master_key);
        let chain_code_hash = chain_hasher.finalize();
        let mut chain_code = [0u8; CHAIN_CODE_LENGTH];
        chain_code.copy_from_slice(&chain_code_hash);
        
        Ok(ExtendedKey {
            key_left,
            key_right,
            chain_code,
            public_key,
        })
    }
    
    pub fn derive_child(&self, index: u32) -> Result<ExtendedKey, KeyDerivationError> {
        let is_hardened = index >= HARDENED_OFFSET;
        
        let mut hmac_input = Vec::new();
        if is_hardened {
            hmac_input.push(0x00);
            hmac_input.extend_from_slice(&self.key_left);
            hmac_input.extend_from_slice(&self.key_right);
        } else {
            hmac_input.push(0x02);
            hmac_input.extend_from_slice(self.public_key.as_bytes());
        }
        hmac_input.extend_from_slice(&index.to_be_bytes());
        
        let mut mac = HmacSha512::new_from_slice(&self.chain_code)
            .map_err(|_| KeyDerivationError::CryptoError)?;
        mac.update(&hmac_input);
        let hmac_result = mac.finalize().into_bytes();
        
        let mut kl_new = [0u8; 32];
        let mut kr_new = [0u8; 32];
        kl_new.copy_from_slice(&hmac_result[..32]);
        kr_new.copy_from_slice(&hmac_result[32..]);
        
        if (kl_new[31] & 0x20) != 0 {
            return Err(KeyDerivationError::InvalidKey);
        }
        
        Self::clamp_scalar(&mut kl_new);
        
        let child_scalar = Scalar::from_bytes_mod_order(kl_new);
        let child_public_point = &child_scalar * &ED25519_BASEPOINT_POINT;
        let child_public_key = VerifyingKey::from_bytes(&child_public_point.compress().to_bytes())
            .map_err(|_| KeyDerivationError::CryptoError)?;
        
        let mut chain_hasher = Sha256::new();
        chain_hasher.update(&[0x01]);
        chain_hasher.update(&hmac_result);
        let chain_code_hash = chain_hasher.finalize();
        let mut child_chain_code = [0u8; CHAIN_CODE_LENGTH];
        child_chain_code.copy_from_slice(&chain_code_hash);
        
        Ok(ExtendedKey {
            key_left: kl_new,
            key_right: kr_new,
            chain_code: child_chain_code,
            public_key: child_public_key,
        })
    }
    
    pub fn signing_key(&self) -> SigningKey {
        SigningKey::from_bytes(&self.key_left)
    }

    /// Generate deterministic chain code for non-hardened derivation
    /// This uses the same logic as the client-side PublicExtendedKey
    pub fn generate_deterministic_chain_code_for_query(&self, query: &str) -> [u8; 32] {
        PublicExtendedKey::generate_deterministic_chain_code(&self.public_key, query)
    }

    fn clamp_scalar(scalar: &mut [u8; 32]) {
        scalar[0] &= 248;
        scalar[31] &= 127;
        scalar[31] |= 64;
    }
}

/// Deterministic key retry logic that uses a query string to find the first working index
pub struct DeterministicKeyFinder;

impl DeterministicKeyFinder {
    /// Find the first valid key index for a given query string
    ///
    /// # Arguments
    /// * `query` - The query string (e.g., "user-data", "messages", etc.)
    /// * `derive_fn` - Function that attempts to derive a key at a given index
    /// * `max_attempts` - Maximum number of attempts (default: 1000)
    ///
    /// # Returns
    /// The first valid index and the derived key, or an error if none found
    pub fn find_valid_key<T, F>(
        query: &str,
        derive_fn: F,
        max_attempts: Option<u32>,
    ) -> Result<(u32, T), KeyDerivationError>
    where
        F: Fn(u32) -> Result<T, KeyDerivationError>,
    {
        let max_attempts = max_attempts.unwrap_or(1000);

        // Hash the query string to get a deterministic starting point
        let mut hasher = Sha256::new();
        hasher.update(query.as_bytes());
        let hash = hasher.finalize();

        // Use first 4 bytes of hash as starting index
        let start_index = u32::from_le_bytes([hash[0], hash[1], hash[2], hash[3]]) % HARDENED_OFFSET;

        // Try indices starting from the hash-derived index
        for attempt in 0..max_attempts {
            let index = start_index.wrapping_add(attempt);

            match derive_fn(index) {
                Ok(key) => {
                    println!("   Found valid key for '{}' at index {} (attempt {})", query, index, attempt + 1);
                    return Ok((index, key));
                }
                Err(KeyDerivationError::InvalidKey) => {
                    // Continue to next index
                    continue;
                }
                Err(e) => {
                    // Other errors are not retryable
                    return Err(e);
                }
            }
        }

        Err(KeyDerivationError::InvalidKey)
    }

    /// Find valid key for root key generation from seed
    pub fn find_valid_root_key(base_seed: &[u8], query: &str) -> Result<(u32, ExtendedKey), KeyDerivationError> {
        Self::find_valid_key(query, |counter| {
            let mut extended_seed = base_seed.to_vec();
            extended_seed.extend_from_slice(&counter.to_le_bytes());
            ExtendedKey::from_seed(&extended_seed)
        }, None)
    }

    /// Find valid key for child derivation
    pub fn find_valid_child_key(
        parent: &ExtendedKey,
        query: &str,
        hardened: bool
    ) -> Result<(u32, ExtendedKey), KeyDerivationError> {
        let hardened_offset = if hardened { HARDENED_OFFSET } else { 0 };

        Self::find_valid_key(query, |index| {
            parent.derive_child(hardened_offset + index)
        }, None)
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    println!("=== Hierarchical Pkarr Key Management ===\n");
    
    // Step 1: Generate root pkarr keypair
    let pkarr_keypair = Keypair::random();
    println!("1. Root pkarr keypair: {}", pkarr_keypair.public_key().to_z32());
    
    // Step 2: Derive query master key using deterministic key finding
    let seed = pkarr_keypair.secret_key();

    // Find valid root key using deterministic method
    let (_root_counter, root_key) = DeterministicKeyFinder::find_valid_root_key(&seed, "root")?;
    println!("2. Found valid root key using deterministic method");

    // Find valid query master key using deterministic method
    let (query_master_index, query_master_key) = DeterministicKeyFinder::find_valid_child_key(&root_key, "query-master", true)?;
    println!("3. Query master key (m/{}'): {}", query_master_index, hex::encode(query_master_key.public_key.as_bytes()));
    
    // Step 3: Publish query master public key to pkarr
    let pkarr_client = PkarrClient::new()?;
    pkarr_client.publish_query_key(
        &pkarr_keypair,
        &query_master_key.public_key,
        Some("query-master"),
    ).await?;
    println!("4. ✓ Published query master public key to pkarr");
    
    // Step 5: Derive specific purpose keys using deterministic non-hardened derivation
    let purposes = vec![
        "user-data",
        "messages",
        "metadata",
        "files",
        "social",
    ];

    println!("\n5. Deriving purpose-specific keys (deterministic non-hardened):");

    let mut successful_purposes = Vec::new();

    for purpose in &purposes {
        match DeterministicKeyFinder::find_valid_child_key(&query_master_key, purpose, false) {
            Ok((index, purpose_key)) => {
                println!("   ✓ {}: m/{}'/{} → {}",
                    purpose,
                    query_master_index,
                    index,
                    hex::encode(purpose_key.public_key.as_bytes())[..16].to_string() + "..."
                );

                // Publish some example data under this purpose key
                let purpose_keypair = pkarr::Keypair::from_secret_key(&purpose_key.signing_key().to_bytes());

                // Publish a simple record indicating this purpose
                pkarr_client.publish_query_key(
                    &purpose_keypair,
                    &purpose_key.public_key, // Self-reference for demo
                    Some("info"),
                ).await?;

                println!("     → Published example record at: {}", purpose_keypair.public_key().to_z32());

                successful_purposes.push((purpose.to_string(), index, purpose_keypair.public_key()));
            }
            Err(e) => {
                println!("   ✗ {}: Failed - {}", purpose, e);
            }
        }
    }
    
    println!("\n=== Security Model ===");
    println!("🔒 Server Security:");
    println!("   • Has root pkarr private key → can derive all keys");
    println!("   • Has query master private key → can derive all purpose keys");
    println!("   • Can publish/update records for any purpose");
    
    println!("\n🔓 Client Capabilities:");
    println!("   • Has root pkarr public key → can resolve query master");
    println!("   • Has query master public key → can derive purpose public keys");
    println!("   • Can resolve records for any purpose");
    println!("   • CANNOT derive any private keys (hardened protection)");
    println!("   • CANNOT overwrite any records (no private keys)");
    
    println!("\n=== Client Workflow ===");
    println!("1. Client gets root pkarr public key: {}", pkarr_keypair.public_key().to_z32());
    println!("2. Client resolves query master from pkarr DNS");
    println!("3. Client derives purpose public keys: m/0, m/1, m/2, ...");
    println!("4. Client resolves specific records using derived keys");
    
    println!("\n=== Deterministic Chain Code Logic ===");
    println!("🔗 Hardcoded chaincode implementation:");
    println!("   • Chain code = SHA256(query_master_public_key || query_string)");
    println!("   • Both server and client can compute this independently");
    println!("   • No need to publish chain code in pkarr DNS records");
    println!("   • Deterministic and collision-resistant");

    // Demonstrate the deterministic chain code generation
    let demo_chain_code = query_master_key.generate_deterministic_chain_code_for_query("hierarchical-derivation");
    println!("   • Example: chain code for 'hierarchical-derivation' = {}", hex::encode(&demo_chain_code));

    println!("\n=== Deterministic Key Finding ===");
    println!("🎯 Query-based deterministic derivation:");
    println!("   • Each query string (e.g., 'user-data') is hashed with SHA256");
    println!("   • Hash provides deterministic starting index for key search");
    println!("   • Same query always produces same key (reproducible)");
    println!("   • Different queries produce different keys (collision-resistant)");

    println!("\n=== Example Use Cases ===");
    println!("• Social app: Derive keys for posts, messages, profile, friends");
    println!("• File storage: Derive keys for documents, images, backups, shared");
    println!("• Identity: Derive keys for credentials, attestations, proofs, metadata");
    println!("• Messaging: Derive keys for contacts, groups, channels, archives");

    println!("\n✅ Hierarchical pkarr key management setup complete!");
    println!("   Run the pkarr_client example to see client-side resolution");
    
    Ok(())
}
